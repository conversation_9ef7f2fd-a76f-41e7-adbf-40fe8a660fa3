package com.holderzone.member.openapi.controller;

import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.dto.coupon.MemberCouponStoreListDTO;
import com.holderzone.member.common.feign.MemberBaseFeign;
import com.holderzone.member.common.qo.coupon.MemberCouponDetailQO;
import com.holderzone.member.common.qo.coupon.MemberCouponListQO;
import com.holderzone.member.common.qo.coupon.MemberCouponNumQO;
import com.holderzone.member.common.qo.coupon.MemberCouponQrcodeQO;
import com.holderzone.member.common.qo.growth.AppletStoreListQO;
import com.holderzone.member.common.vo.coupon.CouponQrCodeVO;
import com.holderzone.member.common.vo.coupon.MemberCouponWxCountVO;
import com.holderzone.member.common.vo.coupon.MemberCouponWxDetailVO;
import com.holderzone.member.common.vo.coupon.MemberCouponWxVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 会员优惠券api
 *
 * <AUTHOR>
 * @date 2025/4/7
 */
@RestController
@RequestMapping("/coupon")
@Slf4j
public class MemberCouponController {

    @Resource
    private MemberBaseFeign memberBaseFeign;

    /**
     * 按时间分页查询已领券
     *
     * @param qo 查询参数
     * @return 券列表
     */
    @PostMapping("/pageMemberCouponByTime")
    public Result<List<MemberCouponWxVO>> pageMemberCouponByTime(@RequestBody MemberCouponListQO qo) {
        return memberBaseFeign.pageMemberCouponByTime(qo);
    }

    /**
     * 优惠券详情
     *
     * @param qo 查询参数
     * @return 优惠券详情
     */
    @PostMapping("/detail")
    public Result<MemberCouponWxDetailVO> detail(@RequestBody MemberCouponDetailQO qo) {
        return memberBaseFeign.detail(qo.getMemberCouponLinkGuid());
    }

    /**
     * 优惠券二维码
     *
     * @param qo 查询参数
     * @return 优惠券详情
     */
    @PostMapping("/getQrcode")
    public Result<CouponQrCodeVO> getQrCode(@RequestBody MemberCouponQrcodeQO qo) {
        return memberBaseFeign.getQrCode(qo.getMemberCouponLinkGuid());
    }

    /**
     * 优惠券二维码
     *
     * @param qo 查询参数
     * @return 优惠券详情
     */
    @PostMapping("/count")
    public Result<MemberCouponWxCountVO> countMemberNum(@RequestBody MemberCouponNumQO qo) {
        return memberBaseFeign.countMemberNum(qo);
    }

    /**
     * 查询优惠卷适用门店
     *
     * @param dto 查询条件
     * @return 查询结果
     */
    @PostMapping("/getMemberCouponStoreList")
    public Result getMemberCouponStoreList(@RequestBody MemberCouponStoreListDTO dto) {
        return memberBaseFeign.getMemberCouponStoreList(dto);
    }

}
